#!/usr/bin/env python3
"""
Simulate unified LLM prompt processing for complex JCET KUI format
"""

def simulate_jcet_kui_parsing():
    """Simulate unified LLM parsing for JCET KUI format email"""
    
    # Test email
    subject = "FW: KUID11M013-D007+ KUID08M026-D006 test high yield 99.9% please confirm continuation"
    
    print("=== Simulating Unified LLM Parsing for Complex JCET KUI Format ===")
    print(f"Subject: {subject}")
    print(f"Sender: <EMAIL>")
    
    print("\nJCET Parsing Rules in Unified Prompt:")
    print("1. JCET identifiers: 'jcet', 'zhixin', 'chipson', 'taiwan', 'copper wire'")
    print("2. kui_pattern_long: KUI + length>4, take first 15 chars as LOT (batch number)")
    print("3. gyc_pattern_long: GYC + length>4, take first 15 chars as LOT (batch number)")
    print("4. kui_pattern_short: KUI + length<=4, positional parsing")
    print("5. yield_rate: find percentage numbers")
    
    print("\nAnalysis Process:")
    
    # 1. Vendor identification
    print("Step 1: Vendor Identification")
    print("- Email contains 'test' and KUI patterns")
    print("- Format matches JCET KUI mode")
    print("-> Vendor: JCET")
    
    # 2. KUI pattern parsing
    print("\nStep 2: KUI Pattern Parsing")
    kui_items = ["KUID11M013-D007+", "KUID08M026-D006"]
    
    for item in kui_items:
        print(f"- Found: {item}")
        print(f"  - Length: {len(item)} > 4")
        print(f"  - Matches kui_pattern_long rule")
        print(f"  - First 15 chars: {item[:15]}")
    
    # 3. Yield extraction
    print("\nStep 3: Yield Extraction")
    print("- Found: 'yield 99.9%'")
    print("- Extracted yield: 99.9%")
    
    # 4. Final parsing result
    print("\nUnified LLM Expected Parsing Result:")
    result = {
        "vendor_code": "JCET",
        "vendor_name": "JCET",
        "mo_number": None,  # No MO identified in this email
        "lot_number": "KUID11M013-D00",  # First KUI item's first 15 chars as LOT
        "yield_rate": 99.9,
        "confidence_score": 0.85,
        "parsing_methods_used": ["kui_pattern_long", "yield_extraction"],
        "analysis_reasoning": "Based on JCET KUI long pattern rule, KUID11M013-D007+ length 16>4, take first 15 chars as LOT (batch number). Found 99.9% yield rate."
    }
    
    for key, value in result.items():
        print(f"- {key}: {value}")
    
    print("\nAdvantages of Method 2 Integration:")
    print("✓ Preserves all 9 JCET parsing modes")
    print("✓ Supports KUI/GYC long/short pattern handling")
    print("✓ Unified prompt contains all vendor rules")
    print("✓ Switch between ollama/grok via .env")
    print("✓ Single prompt handles all complex formats")
    
    print("\nOriginal Email Processing:")
    original_subject = "FW: KUID11M013-D007+ KUID08M026-D006测试高良 测试良率99.9% 请帮忙确认是否可以续流"
    print("Chinese version would be processed the same way:")
    print("- KUID11M013-D007+ -> LOT: KUID11M013-D00 (batch number)")
    print("- KUID08M026-D006 -> additional LOT or backup batch")
    print("- 测试良率99.9% -> yield: 99.9%")
    print("- Vendor: JCET (identified by Chinese test keywords)")
    print("- Key insight: KUID開頭都是批次編號(LOT)，不是製造訂單(MO)")

if __name__ == "__main__":
    simulate_jcet_kui_parsing()