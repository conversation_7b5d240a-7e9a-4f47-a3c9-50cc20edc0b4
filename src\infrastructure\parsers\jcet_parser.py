"""
JCET 廠商解析器實作
基於 VBA JCETInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：InStr(LCase(body), "jcet") Or InStr(LCase(senderAddress), "jcetglobal.com")
- 解析規則：尋找包含 KUI/GYC 的字串，長度 > 4 取前 15 字符作為 MO
- 若長度 <= 4，記錄位置，後面的詞作為 lot 和 product
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult


class JCETParser(VendorParser):
    """
    JCET 廠商郵件解析器
    
    識別條件：
    - 郵件正文包含 "jcet"
    - 寄件者地址包含 "jcetglobal.com"
    
    解析機制：
    1. 尋找包含 KUI 或 GYC 的詞
    2. 如果詞長度 > 4：取前 15 字符作為 MO 編號
    3. 如果詞長度 <= 4：該詞後的第一個詞作為 lot，第二個詞作為 product
    """
    
    def __init__(self):
        """初始化 JCET 解析器"""
        super().__init__()
        self._vendor_code = "JCET"
        self._vendor_name = "JCET"
        self._identification_patterns = [
            "jcet",              # 內文關鍵字
            "jcetglobal.com",    # 寄件者域名
            "致新",              # 中文公司名稱
            "chipson",           # 英文公司名稱
            "taiwan"             # 台灣關鍵字
        ]
        self.set_confidence_threshold(0.8)
        
        # JCET 特有的 MO 編號模式
        self.mo_patterns = [
            r'\b\w*KUI\w*\b',      # 包含 'KUI' 的詞
            r'\b\w*GYC\w*\b'       # 包含 'GYC' 的詞
        ]

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        content = (subject + " " + body).lower()
        
        # 檢查所有識別模式
        for pattern in self._identification_patterns:
            if pattern.lower() in content:
                matched_patterns.append(pattern)
                confidence_score += 0.8  # 每個模式增加信心分數以達到閾值
        
        # VBA: InStr(1, LCase(body), "jcet", vbTextCompare) > 0
        if "jcet" in body.lower():
            confidence_score += 0.4
            
        # 也檢查主旨中的 jcet（增強識別能力）
        if "jcet" in subject.lower():
            confidence_score += 0.3
            
        # VBA: InStr(1, LCase(senderAddress), "jcetglobal.com", vbTextCompare) > 0
        if "jcetglobal.com" in sender_lower:
            matched_patterns.append("jcetglobal.com")
            confidence_score += 0.6
        
        # 額外的信心分數計算
        if "jcet" in sender_lower:
            confidence_score += 0.3  # JCET 相關寄件者
            
        # 檢查主旨是否有 KUI/GYC 模式（這是 JCET 的強識別特徵）
        kui_gyc_found = False
        for pattern in self.mo_patterns:
            if re.search(pattern, subject, re.IGNORECASE):
                confidence_score += 0.5  # KUI/GYC 是 JCET 強特徵，提高分數
                matched_patterns.append("KUI/GYC_pattern")
                kui_gyc_found = True
                break
        
        # 如果找到 KUI/GYC 模式，即使沒有其他 JCET 關鍵字也認為是 JCET
        if kui_gyc_found:
            confidence_score = max(confidence_score, 0.8)  # 確保達到識別閾值
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="jcet_keyword_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        if not email_data.subject:
            # 空主旨時嘗試從識別結果判斷
            identification = self.identify_vendor(email_data)
            if not identification.is_identified:
                raise ParsingError("Empty subject and cannot identify vendor", vendor_code=self.vendor_code)
        
        try:
            # 使用 JCET 關鍵字解析機制 - 擴展支援郵件內文
            subject = email_data.subject or ""
            body = email_data.body or ""
            
            # 首先嘗試從主旨解析
            jcet_result = self.parse_jcet_keywords(subject)
            
            # 如果主旨解析不完整，嘗試從郵件內文解析補充信息
            if not jcet_result["product"] or not jcet_result["lot_number"]:
                body_result = self.parse_jcet_from_body(body, subject)
                
                # 合併解析結果，優先使用內文的完整信息
                if body_result["product"] and not jcet_result["product"]:
                    jcet_result["product"] = body_result["product"]
                if body_result["lot_number"] and not jcet_result["lot_number"]:
                    jcet_result["lot_number"] = body_result["lot_number"]
                if body_result["mo_number"] and not jcet_result["mo_number"]:
                    jcet_result["mo_number"] = body_result["mo_number"]
                if body_result["method"] != "no_pattern":
                    jcet_result["method"] = body_result["method"] + "_from_body"
            
            # MO 編號直接使用解析結果，不做額外格式檢查
            mo_number = jcet_result["mo_number"] if jcet_result["mo_number"] else None
            
            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=jcet_result["product"] if jcet_result["product"] != "" else None,
                mo_number=mo_number,
                lot_number=jcet_result["lot_number"] if jcet_result["lot_number"] != "" else None,
                is_success=True,
                error_message=None,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': jcet_result["product"],
                    'lot_number': jcet_result["lot_number"],
                    'mo_number': jcet_result["mo_number"],  # 原始 MO 編號
                    'parsing_method': jcet_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'jcet_kui_gyc_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                extraction_method="traditional",
                is_success=False,
                error_message=f"JCET parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_jcet_keywords(self, subject: str) -> Dict[str, Any]:
        """
        解析 JCET 關鍵字：KUI/GYC 模式 + LOT 編號模式
        基於 VBA JCETInfoFromStrings 邏輯並支援多MO解析
        
        更新邏輯：
        - 尋找所有包含 KUI 或 GYC 的模式
        - MO編號遇到逗號或空格停止，不限制15字符
        - 支援多個MO，用逗號分隔
        - 新增：尋找 lot：XXX 或 测试批号：XXX 模式
        """
        if not subject:
            return {
                "mo_number": "",
                "lot_number": "",
                "product": "",
                "method": "no_pattern"
            }
        
        mo_numbers = []
        lot_number = ""
        product = ""
        method = "no_pattern"
        
        # 第一階段：使用正則表達式尋找所有KUI/GYC模式
        # 匹配 KUI/GYC 開頭，直到遇到逗號、空格、+號或其他分隔符
        kui_pattern = r'\b(KUI[A-Z0-9\-]+?)(?=[,\s\+\u4e00-\u9fff]|$)'
        gyc_pattern = r'\b(GYC[A-Z0-9\-]+?)(?=[,\s\+\u4e00-\u9fff]|$)'
        
        # 尋找所有KUI模式
        kui_matches = re.findall(kui_pattern, subject, re.IGNORECASE)
        if kui_matches:
            mo_numbers.extend(kui_matches)
            method = "kui_pattern_multi"
        
        # 尋找所有GYC模式
        gyc_matches = re.findall(gyc_pattern, subject, re.IGNORECASE)
        if gyc_matches:
            mo_numbers.extend(gyc_matches)
            if method == "kui_pattern_multi":
                method = "kui_gyc_pattern_multi"
            else:
                method = "gyc_pattern_multi"
        
        # 如果找到多個MO，用逗號分隔
        mo_number = ",".join(mo_numbers) if mo_numbers else ""
        
        # 如果找到MO，直接返回結果
        if mo_number:
            return {
                "mo_number": mo_number,
                "lot_number": lot_number,
                "product": product,
                "method": method
            }
        
        # 第二階段：原有的短模式邏輯（向後兼容）
        words = subject.split()
        
        # 尋找短模式的 KUI/GYC（長度 <= 4）
        for i, word in enumerate(words):
            word_upper = word.upper()
            if len(word) <= 4:
                if word_upper == "KUI" or word_upper == "GYC":
                    method = f"{word_upper.lower()}_pattern_short"
                    # 取下一個詞作為LOT
                    if i + 1 < len(words):
                        lot_number = words[i + 1]
                        mo_number = lot_number  # 短模式中LOT作為MO
                    # 取再下一個詞作為產品
                    if i + 2 < len(words):
                        product = words[i + 2]
                    break
        
        # 如果短模式找到結果，返回
        if mo_number:
            return {
                "mo_number": mo_number,
                "lot_number": lot_number,
                "product": product,
                "method": method
            }
        
        # 第三階段：LOT 編號解析模式（適用於沒有 KUI/GYC 的郵件）
        # 尋找 "lot：XXX" 或 "lot: XXX" 模式
        lot_match = re.search(r'lot[：:]\s*([A-Z0-9.]+)', subject, re.IGNORECASE)
        if lot_match:
            lot_number = lot_match.group(1)
            mo_number = lot_number  # 將 lot 編號作為 MO 編號
            method = "lot_pattern"
        
        # 尋找 "测试批号：XXX" 模式
        test_batch_match = re.search(r'测试批号[：:]\s*([A-Z0-9.]+)', subject, re.IGNORECASE)
        if test_batch_match:
            test_batch_number = test_batch_match.group(1)
            # 如果沒有找到 lot 編號，使用测试批号
            if not lot_number:
                lot_number = test_batch_number
                mo_number = test_batch_number
                method = "test_batch_pattern"
            
        # 尋找產品型號 (如 G2892K21D)
        product_match = re.search(r'(G\d{4}[A-Z]\d{2}[A-Z])', subject, re.IGNORECASE)
        if product_match:
            product = product_match.group(1)
            if method == "no_pattern":
                method = "product_pattern"
        
        return {
            "mo_number": mo_number,
            "lot_number": lot_number,
            "product": product,
            "method": method
        }
    
    def parse_jcet_from_body(self, body: str, subject: str = "") -> Dict[str, Any]:
        """
        從郵件內文解析 JCET 信息
        支援表格格式和結構化數據的解析
        
        解析模式：
        1. 產品型號：TNVP163201 等格式
        2. LOT編號：DNHC0.1;2TFX02077.1 等格式 
        3. MO編號：KUID11M013-D007 等格式
        4. 表格結構識別
        """
        if not body:
            return {
                "mo_number": "",
                "lot_number": "", 
                "product": "",
                "method": "no_body_content"
            }
        
        mo_number = ""
        lot_number = ""
        product = ""
        method = "no_pattern"
        
        # 清理郵件內文，移除HTML標籤和多餘空白
        import html
        clean_body = html.unescape(body)
        clean_body = re.sub(r'<[^>]+>', ' ', clean_body)  # 移除HTML標籤
        clean_body = re.sub(r'\s+', ' ', clean_body)  # 合併多個空白
        
        # 模式1：產品型號識別 (TNVP163201, DNHC, 等)
        # 尋找像產品編號的模式：字母開頭，可包含數字和點
        product_patterns = [
            r'\b([A-Z]{2,6}[0-9]{3,8})\b',        # TNVP163201 格式
            r'\b([A-Z]{2,4}[0-9]{1,4}\.[0-9])\b', # DNHC0.1 格式  
            r'\b([A-Z]{3,5}[A-Z0-9]{3,8})\b'      # 其他字母數字組合
        ]
        
        for pattern in product_patterns:
            matches = re.findall(pattern, clean_body, re.IGNORECASE)
            if matches:
                # 選擇最可能是產品編號的匹配（排除KUI/GYC）
                for match in matches:
                    if not re.match(r'KUI|GYC', match, re.IGNORECASE):
                        product = match
                        method = "product_pattern_from_body"
                        break
                if product:
                    break
        
        # 模式2：LOT編號識別
        # 尋找分號分隔的批號格式：DNHC0.1;2TFX02077.1
        lot_patterns = [
            r'\b([A-Z]{2,4}[0-9]+\.[0-9]+(?:;[A-Z0-9.]+)*)\b',  # DNHC0.1;2TFX02077.1
            r'\b([A-Z0-9]{4,12}\.[0-9]+)\b',                     # 一般點號格式
            r'批[号號][：:]\s*([A-Z0-9.;]+)',                    # 中文批號標記
            r'lot[：:]\s*([A-Z0-9.;]+)'                          # 英文lot標記
        ]
        
        for pattern in lot_patterns:
            match = re.search(pattern, clean_body, re.IGNORECASE)
            if match:
                potential_lot = match.group(1)
                # 避免將KUI/GYC誤認為LOT
                if not re.match(r'KUI|GYC', potential_lot, re.IGNORECASE):
                    lot_number = potential_lot
                    if method == "no_pattern":
                        method = "lot_pattern_from_body"
                    else:
                        method += "_with_lot"
                    break
        
        # 模式3：MO編號識別 (優先級最高)
        # 從主旨或內文尋找KUI/GYC模式
        mo_patterns = [
            r'\b(KUI[A-Z0-9\-]+)\b',
            r'\b(GYC[A-Z0-9\-]+)\b'
        ]
        
        # 優先從主旨尋找MO
        search_text = subject + " " + clean_body
        mo_matches = []
        
        for pattern in mo_patterns:
            matches = re.findall(pattern, search_text, re.IGNORECASE)
            mo_matches.extend(matches)
        
        if mo_matches:
            # 去重並排序
            unique_mo = list(dict.fromkeys(mo_matches))  # 保持順序去重
            mo_number = ",".join(unique_mo)
            method = "mo_pattern_from_body" if method == "no_pattern" else method + "_with_mo"
        
        # 模式4：表格結構識別增強
        # 尋找表格中的結構化數據
        if not product or not lot_number:
            # 尋找連續的結構化數據行
            lines = clean_body.split('\n')
            for i, line in enumerate(lines):
                line = line.strip()
                # 如果這行包含產品編號樣式的文字
                if re.search(r'\b[A-Z]{2,6}[0-9]{3,8}\b', line) and not product:
                    product_match = re.search(r'\b([A-Z]{2,6}[0-9]{3,8})\b', line)
                    if product_match and not re.match(r'KUI|GYC', product_match.group(1), re.IGNORECASE):
                        product = product_match.group(1)
                        method = method + "_table_structure" if method != "no_pattern" else "table_structure"
                
                # 尋找下一行是否有LOT格式
                if i + 1 < len(lines) and not lot_number:
                    next_line = lines[i + 1].strip()
                    lot_match = re.search(r'\b([A-Z0-9]{4,12}\.[0-9]+(?:;[A-Z0-9.]+)*)\b', next_line)
                    if lot_match and not re.match(r'KUI|GYC', lot_match.group(1), re.IGNORECASE):
                        lot_number = lot_match.group(1)
                        method = method + "_table_lot" if method != "no_pattern" else "table_lot"
        
        return {
            "mo_number": mo_number,
            "lot_number": lot_number,
            "product": product,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'KUI pattern matching (long: >4 chars)',
                'GYC pattern matching (long: >4 chars)',
                'KUI pattern matching (short: <=4 chars)',
                'GYC pattern matching (short: <=4 chars)'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'based_on': 'VBA JCETInfoFromStrings',
            'extraction_capabilities': [
                'MO number from KUI/GYC pattern (long mode: first 15 chars)',
                'LOT number from position after KUI/GYC (short mode)',
                'Product name from position after LOT (short mode)',
                'Case-insensitive keyword matching',
                'Email body and sender pattern identification'
            ],
            'special_features': [
                'Dual pattern matching (long/short mode)',
                'Position-based extraction for short patterns',
                'Length-based MO extraction for long patterns',
                'Multiple identification methods (body + sender)'
            ]
        }