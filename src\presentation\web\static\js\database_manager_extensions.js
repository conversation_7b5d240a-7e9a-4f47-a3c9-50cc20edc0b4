/**
 * 資料庫管理器擴展功能
 * 包含全選、進階搜尋、批次操作等功能
 */

class DatabaseManagerExtensions {
    constructor(databaseManager) {
        this.dbManager = databaseManager;
        this.initExtensions();
    }

    /**
     * 初始化擴展功能
     */
    initExtensions() {
        this.bindExtensionEvents();
    }

    /**
     * 綁定擴展事件
     */
    bindExtensionEvents() {
        // 全選功能
        $('#select-all-checkbox').on('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });
        
        // 進階搜尋功能
        $('#advanced-search-btn').on('click', () => {
            this.toggleAdvancedSearch();
        });
        
        $('#apply-filters-btn').on('click', () => {
            this.applyAdvancedFilters();
        });
        
        $('#reset-filters-btn').on('click', () => {
            this.resetAdvancedFilters();
        });
        
        $('#clear-filters-btn').on('click', () => {
            this.clearAllFilters();
        });
    }

    /**
     * 全選切換
     */
    toggleSelectAll(checked) {
        $('.row-select').prop('checked', checked);
        
        if (checked) {
            // 全選時更新計數
            const totalRows = $('.row-select').length;
            this.dbManager.showBatchActionsPanel(totalRows);
        } else {
            // 取消全選時隱藏操作面板
            this.dbManager.hideBatchActionsPanel();
        }
    }
    
    /**
     * 切換進階搜尋面板
     */
    toggleAdvancedSearch() {
        const panel = $('#advanced-search-panel');
        const btn = $('#advanced-search-btn');
        
        if (panel.hasClass('hidden')) {
            panel.removeClass('hidden');
            btn.text('🔼 收起搜尋');
        } else {
            panel.addClass('hidden');
            btn.text('🔍 進階搜尋');
        }
    }
    
    /**
     * 應用進階篩選
     */
    applyAdvancedFilters() {
        if (!this.dbManager.dataTable) return;
        
        const filters = {
            sender: $('#filter-sender').val(),
            subject: $('#filter-subject').val(),
            vendor: $('#filter-vendor').val(),
            parseStatus: $('#filter-parse-status').val(),
            product: $('#filter-product').val(),
            mo: $('#filter-mo').val()
        };
        
        // 應用 DataTable 搜尋篩選
        let searchTerms = [];
        
        if (filters.sender) searchTerms.push(filters.sender);
        if (filters.subject) searchTerms.push(filters.subject);
        if (filters.vendor) searchTerms.push(filters.vendor);
        if (filters.parseStatus) searchTerms.push(filters.parseStatus);
        if (filters.product) searchTerms.push(filters.product);
        if (filters.mo) searchTerms.push(filters.mo);
        
        this.dbManager.dataTable.search(searchTerms.join(' ')).draw();
        
        // 更新篩選狀態顯示
        this.updateFilterStatus(filters);
    }
    
    /**
     * 重置進階篩選
     */
    resetAdvancedFilters() {
        $('#filter-sender').val('');
        $('#filter-subject').val('');
        $('#filter-vendor').val('');
        $('#filter-parse-status').val('');
        $('#filter-product').val('');
        $('#filter-mo').val('');
        
        this.clearAllFilters();
    }
    
    /**
     * 清除所有篩選
     */
    clearAllFilters() {
        if (this.dbManager.dataTable) {
            this.dbManager.dataTable.search('').draw();
        }
        
        // 清除搜尋輸入框
        $('#search-input').val('');
        $('#search-results-info').addClass('hidden');
        
        // 重置全選狀態
        $('#select-all-checkbox').prop('checked', false);
        this.dbManager.hideBatchActionsPanel();
    }
    
    /**
     * 更新篩選狀態顯示
     */
    updateFilterStatus(filters) {
        const activeFilters = Object.values(filters).filter(v => v);
        const infoDiv = $('#search-results-info');
        
        if (activeFilters.length > 0) {
            infoDiv.removeClass('hidden');
            $('#search-results-text').text(`已套用 ${activeFilters.length} 個篩選條件`);
        } else {
            infoDiv.addClass('hidden');
        }
    }
}