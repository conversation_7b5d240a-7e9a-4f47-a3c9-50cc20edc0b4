"""
統一 LLM 客戶端
支持 Ollama 和 Grok，通過 .env 配置切換
整合方法2的分類器規則，提供統一的解析接口
"""

import json
import os
import re
import requests
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from dotenv import load_dotenv

# 加載環境變數
load_dotenv()

from src.infrastructure.logging.logger_manager import LoggerManager
from src.infrastructure.adapters.llm.grok_client import GrokClient, GrokRequest


class LLMProvider(Enum):
    """LLM 提供者類型"""
    OLLAMA = "ollama"
    GROK = "grok"


@dataclass
class UnifiedLLMResult:
    """統一 LLM 解析結果"""
    vendor_code: Optional[str] = None
    vendor_name: Optional[str] = None
    product_code: Optional[str] = None
    lot_number: Optional[str] = None
    mo_number: Optional[str] = None
    yield_rate: Optional[float] = None
    test_batch: Optional[str] = None
    device_type: Optional[str] = None
    quantity: Optional[str] = None
    
    # 解析詳情
    confidence_score: float = 0.0
    extraction_method: str = "unified_llm"
    parsing_methods_used: List[str] = None
    analysis_reasoning: str = ""
    raw_response: Optional[str] = None
    error_message: Optional[str] = None
    is_success: bool = False
    
    # 服務資訊
    llm_provider: str = ""
    llm_model: str = ""
    
    def __post_init__(self):
        if self.parsing_methods_used is None:
            self.parsing_methods_used = []


class UnifiedLLMClient:
    """統一 LLM 客戶端"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("UnifiedLLMClient")
        
        # 從環境變數讀取配置
        provider_str = os.getenv('LLM_PROVIDER', 'ollama').lower()
        self.provider = LLMProvider.OLLAMA if provider_str == 'ollama' else LLMProvider.GROK
        
        # 初始化對應的客戶端
        if self.provider == LLMProvider.GROK:
            self.grok_client = GrokClient()
            self.model = os.getenv('GROK_MODEL', 'grok-beta')
            self.client_info = {
                'provider': 'Grok',
                'model': self.model,
                'available': self.grok_client.is_enabled()
            }
        else:
            self.ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
            self.model = os.getenv('OLLAMA_MODEL', 'llama3:latest')
            self.timeout = int(os.getenv('LLM_TIMEOUT', '30'))
            self.max_retries = int(os.getenv('LLM_MAX_RETRIES', '3'))
            self.client_info = {
                'provider': 'Ollama',
                'model': self.model,
                'base_url': self.ollama_base_url,
                'available': self._test_ollama_connection()
            }
        
        self.confidence_threshold = float(os.getenv('LLM_CONFIDENCE_THRESHOLD', '0.7'))
        
        self.logger.info(f"統一 LLM 客戶端初始化: 提供者={self.provider.value}, 模型={self.model}")
    
    def _test_ollama_connection(self) -> bool:
        """測試 Ollama 連接"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [m['name'] for m in models]
                return self.model in model_names
            return False
        except Exception:
            return False
    
    def get_client_info(self) -> Dict[str, Any]:
        """獲取客戶端信息"""
        return self.client_info.copy()
    
    def is_available(self) -> bool:
        """檢查服務是否可用"""
        return self.client_info.get('available', False)
    
    def parse_email(self, subject: str, body: str = "", sender: str = "") -> UnifiedLLMResult:
        """統一解析郵件接口"""
        if not self.is_available():
            return UnifiedLLMResult(
                error_message=f"{self.provider.value} 服務不可用",
                is_success=False,
                llm_provider=self.provider.value,
                llm_model=self.model
            )
        
        try:
            # 構建統一提示詞
            prompt = self._build_unified_prompt(subject, body, sender)
            
            # 根據提供者調用對應服務
            if self.provider == LLMProvider.GROK:
                response = self._call_grok(prompt)
            else:
                response = self._call_ollama(prompt)
            
            if not response:
                return UnifiedLLMResult(
                    error_message=f"{self.provider.value} API 調用失敗",
                    is_success=False,
                    llm_provider=self.provider.value,
                    llm_model=self.model
                )
            
            # 解析回應
            result = self._parse_response(response)
            result.llm_provider = self.provider.value
            result.llm_model = self.model
            result.raw_response = response
            
            return result
            
        except Exception as e:
            self.logger.error(f"統一 LLM 解析失敗: {e}")
            return UnifiedLLMResult(
                error_message=str(e),
                is_success=False,
                llm_provider=self.provider.value,
                llm_model=self.model
            )
    
    def _build_unified_prompt(self, subject: str, body: str, sender: str) -> str:
        """構建統一提示詞"""
        content_preview = body[:1000] if body else ""
        
        prompt_template = """你是一個專業的半導體郵件解析專家。請分析以下郵件內容，識別廠商並提取關鍵資訊。

郵件資訊：
- 主題: {subject}
- 寄件者: {sender}
- 內容: {content_preview}...

廠商識別優先規則：
1. 如果主題包含 "ANF" → 必須是 ETD 廠商
2. 如果主題包含 "jcet", "致新", "chipson" → JCET 廠商
3. 其他按照下列規則識別

支援的廠商和解析規則：

1. JCET (致新) - 識別標誌: "jcet", "致新", "chipson", "taiwan", "铜丝"
   解析方式：
   - **關鍵規則**: 所有KUI/GYC開頭的編號都是MO編號，不是產品編號！
   - **MO編號識別**: 主題或內文中任何以KUI或GYC開頭的編號，如 KUID11M013-D007, GYCF123456789
   - **多MO格式**: 用逗號分隔多個MO，**注意：逗號後不要空格**，如 "KUID11M013-D007,KUID08M026-D006"
   - **MO長度**: 遇到逗號、空格、+號或中文字符停止，不限制15字符
   - **產品型號識別**: 郵件內文中的產品編號，如 TNVP163201 等格式 (字母+數字組合)
   - **LOT編號識別**: 郵件內文中的批號，如 DNHC0.1;2TFX02077.1 或 DNHC0.1 格式
   - **結構化解析**: 支援表格格式，產品型號在上，LOT在下，MO在主題中
   - **示例**: 
     * 主題: "RE: KUID11M013-D007+ KUID08M026-D006测试高良"
     * 內文: "TNVP163201 \n DNHC0.1;2TFX02077.1 \n KUID11M013"
     * 解析: product_code="TNVP163201", lot_number="DNHC0.1", mo_number="KUID11M013-D007,KUID08M026-D006"
   - **絕對不要**: 將KUI/GYC格式放入product_code欄位！
2. GTK (Greatek) - 識別標誌: "ft hold", "ft lot", "gtk", "greatek"
   重要：GTK 廠商的 vendor_code 必須是 "GTK"，不是產品代碼！
   主題格式解析: "Type: product, MO: mo, LOT: lot, yield:rate"
3. ETD (Etrend) - 識別標誌: "anf", "etrendtech"
   重要：如果主題包含 "ANF" 關鍵字，必須識別為 ETD，不是 JCET！
   ANF斜線分隔解析: parts[1]=product, parts[4]=lot, parts[6或7]=mo
4. LINGSEN - 識別標誌: "LINGSEN", "lingsen", "Lowyield", "GMT" + G產品
   解析方式：
   - 產品代碼: G開頭的代碼，包含第一個括號內容，例如 G2518KK1U(CTAXP)，但不包括第二個括號 (G2518XXXXDB1KK1451)
   - MO編號: "Run#" 後的純數字 (如 Run#561144 → 561144)
   - LOT編號: "Lot#" 後的編號 (如 Lot#Q4HK16.1X → Q4HK16.1X)
   - 良率: "LowYield=" 後的百分比
   - 重要: LINGSEN的產品代碼只取到第一個右括號，不包括第二個括號內容
5. XAHT (西安華天) - 識別標誌: "tianshui", "西安"

請以純JSON格式回應，不要包含任何註釋：
{{
  "vendor_code": "廠商代碼",
  "vendor_name": "廠商名稱", 
  "product_code": "產品型號或null (JCET: 絕不包含KUI/GYC格式)",
  "lot_number": "LOT編號或null",
  "mo_number": "MO編號或null (JCET: KUI/GYC格式必須放這裡)",
  "yield_rate": "數字或null",
  "test_batch": "測試批號或null",
  "device_type": "裝置類型或null",
  "quantity": "數量或null",
  "confidence_score": 0.95,
  "parsing_methods_used": ["使用的解析方法"],
  "analysis_reasoning": "詳細分析推理過程",
  "extraction_success": true
}}

重要：回應必須是有效的JSON，不能包含註釋或額外說明文字。"""
        
        final_prompt = prompt_template.format(
            subject=subject,
            sender=sender,
            content_preview=content_preview
        )
        
        return final_prompt
    
    def _call_grok(self, prompt: str) -> Optional[str]:
        """調用 Grok API"""
        try:
            request = GrokRequest(
                prompt=prompt,
                temperature=0.1,
                max_tokens=2000
            )
            
            response = self.grok_client.send_request(request)
            
            if response.success:
                return response.content
            else:
                self.logger.error(f"Grok API 錯誤: {response.error}")
                return None
                
        except Exception as e:
            self.logger.error(f"Grok API 調用失敗: {e}")
            return None
    
    def _call_ollama(self, prompt: str) -> Optional[str]:
        """調用 Ollama API"""
        url = f"{self.ollama_base_url}/api/generate"
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "top_p": 0.9,
                "num_predict": 500
            }
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    url,
                    json=payload,
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get('response', '').strip()
                else:
                    self.logger.warning(f"Ollama API 錯誤 {response.status_code}: {response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Ollama API 超時 (嘗試 {attempt + 1}/{self.max_retries})")
            except Exception as e:
                self.logger.error(f"Ollama API 調用失敗: {e}")
                
            if attempt < self.max_retries - 1:
                time.sleep(2 ** attempt)
        
        return None
    
    def _parse_response(self, response: str) -> UnifiedLLMResult:
        """解析 LLM 回應"""
        try:
            # 提取 JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                raise ValueError("未找到 JSON 格式回應")
            
            json_str = json_match.group(0)
            data = json.loads(json_str)
            
            # 構建結果
            result = UnifiedLLMResult(
                vendor_code=data.get('vendor_code'),
                vendor_name=data.get('vendor_name'),
                product_code=data.get('product_code'),
                lot_number=data.get('lot_number'),
                mo_number=data.get('mo_number'),
                yield_rate=data.get('yield_rate'),
                test_batch=data.get('test_batch'),
                device_type=data.get('device_type'),
                quantity=data.get('quantity'),
                confidence_score=data.get('confidence_score', 0.0),
                parsing_methods_used=data.get('parsing_methods_used', []),
                analysis_reasoning=data.get('analysis_reasoning', ''),
                extraction_method="unified_llm"
            )
            
            # 判斷成功條件
            extraction_success = data.get('extraction_success', False)
            has_core_data = bool(result.vendor_code and (result.product_code or result.lot_number or result.mo_number))
            meets_confidence = result.confidence_score >= self.confidence_threshold
            
            result.is_success = extraction_success and has_core_data and meets_confidence
            
            if not result.is_success:
                result.error_message = f"解析不完整: 成功={extraction_success}, 核心資料={has_core_data}, 信心分數={result.confidence_score}"
            
            return result
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON 解析失敗: {e}")
            return UnifiedLLMResult(
                error_message=f"JSON 解析錯誤: {e}",
                is_success=False
            )
        except Exception as e:
            self.logger.error(f"回應解析失敗: {e}")
            return UnifiedLLMResult(
                error_message=str(e),
                is_success=False
            )