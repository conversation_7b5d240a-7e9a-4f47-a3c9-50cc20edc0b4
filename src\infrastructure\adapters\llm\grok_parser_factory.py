"""
Grok 解析器工廠
整合 Grok 智能解析器到現有的解析器架構中
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from .grok_smart_parser import GrokSmartParser, SmartParsingResult
from .grok_client import GrokClient
from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


class GrokParserFactory:
    """
    Grok 解析器工廠
    提供統一的接口整合 Grok 智能解析功能
    """
    
    def __init__(self):
        """初始化工廠"""
        self.logger = LoggerManager().get_logger("GrokParserFactory")
        
        # 嘗試初始化 Grok 組件
        try:
            self.smart_parser = GrokSmartParser()
            self.grok_client = GrokClient()
            self.grok_available = True
            self.logger.info("Grok 解析器工廠已初始化")
        except Exception as e:
            self.logger.warning(f"Grok 初始化失敗: {e}")
            self.smart_parser = None
            self.grok_client = None
            self.grok_available = False
        
        # 性能統計
        self.performance_stats = {
            "total_requests": 0,
            "successful_parses": 0,
            "failed_parses": 0,
            "avg_response_time": 0.0,
            "grok_api_calls": 0,
            "traditional_fallbacks": 0
        }
    
    def is_grok_available(self) -> bool:
        """檢查 Grok 服務是否可用"""
        return self.grok_available and self.grok_client and self.grok_client.is_enabled()
    
    def _get_service_info(self) -> Dict[str, Any]:
        """獲取 Grok 服務詳細信息"""
        import os
        
        if self.grok_client:
            return {
                'model': os.getenv('GROK_MODEL', 'grok-3-mini'),
                'api_url': os.getenv('GROK_API_URL', 'https://api.x.ai/v1'),
                'version': '1.0',
                'provider': 'X.AI',
                'available': self.is_grok_available(),
                'description': 'X.AI Grok 智能語言模型'
            }
        else:
            return {
                'model': 'N/A',
                'api_url': 'N/A', 
                'version': '1.0',
                'provider': 'X.AI',
                'available': False,
                'description': 'X.AI Grok 智能語言模型（未初始化）'
            }
    
    def parse_email_smart(self, email_data: EmailData) -> EmailParsingResult:
        """
        智能解析郵件
        
        Args:
            email_data: 郵件資料
            
        Returns:
            EmailParsingResult: 解析結果
        """
        start_time = datetime.now()
        self.performance_stats["total_requests"] += 1
        
        # 檢查 Grok 是否可用
        if not self.is_grok_available():
            self.performance_stats["failed_parses"] += 1
            return EmailParsingResult(
                vendor_code="ERROR",
                is_success=False,
                extraction_method="grok_unavailable",
                confidence_score=0.0,
                error_message="Grok 服務不可用"
            )
        
        try:
            # 使用智能解析器
            smart_result = self.smart_parser.parse_email(email_data)
            
            # 轉換為標準結果
            result = self.smart_parser.convert_to_email_parsing_result(smart_result)
            
            # 更新統計
            if result.is_success:
                self.performance_stats["successful_parses"] += 1
            else:
                self.performance_stats["failed_parses"] += 1
            
            # 記錄API調用
            if smart_result.extraction_method == "grok_smart":
                self.performance_stats["grok_api_calls"] += 1
            elif smart_result.extraction_method == "traditional_fallback":
                self.performance_stats["traditional_fallbacks"] += 1
            
            # 計算響應時間
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_avg_response_time(response_time)
            
            self.logger.info(f"智能解析完成 - 廠商: {result.vendor_code}, 成功: {result.is_success}, 方法: {result.extraction_method}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"智能解析失敗: {str(e)}")
            self.performance_stats["failed_parses"] += 1
            
            return EmailParsingResult(
                vendor_code="ERROR",
                is_success=False,
                extraction_method="grok_factory_error",
                confidence_score=0.0,
                error_message=str(e)
            )
    
    def parse_email_with_body_analysis(self, email_data: EmailData) -> EmailParsingResult:
        """
        帶內文分析的解析
        當主題中沒有足夠信息時，深度分析郵件內文
        
        Args:
            email_data: 郵件資料
            
        Returns:
            EmailParsingResult: 解析結果
        """
        # 首先嘗試標準智能解析
        result = self.parse_email_smart(email_data)
        
        # 如果產品型號缺失，嘗試內文分析
        product = result.extracted_data.get('product')
        if result.is_success and not product:
            self.logger.info("產品型號缺失，嘗試內文分析")
            body_result = self._analyze_email_body_for_product(email_data)
            
            if body_result and body_result.get('product'):
                result.extracted_data['product'] = body_result['product']
                result.extraction_method = f"{result.extraction_method}+body_analysis"
                result.extracted_data['body_analysis'] = body_result
                
                self.logger.info(f"從內文提取到產品型號: {body_result['product']}")
        
        return result
    
    def _analyze_email_body_for_product(self, email_data: EmailData) -> Optional[Dict[str, Any]]:
        """分析郵件內文提取產品型號"""
        if not self.grok_client.is_enabled():
            return None
        
        try:
            prompt = f"""
請分析以下郵件內文，提取產品型號(Product Code/PD)：

主題: {email_data.subject}
內文: {email_data.body[:1500]}

請特別注意：
1. 產品型號通常是字母數字組合
2. 可能的格式：G2892K21D, AT1234, M5678等
3. 可能在表格、列表或段落中
4. 可能標記為：Product, PD, 產品型號, 型號等

請以JSON格式回應：
{{
  "product": "產品型號",
  "confidence": 0.95,
  "location": "找到位置的描述",
  "reasoning": "提取推理"
}}

如果沒有找到產品型號，請返回 null。
"""
            
            request = GrokRequest(prompt=prompt, temperature=0.1, max_tokens=500)
            response = self.grok_client.send_request(request)
            
            if response.success:
                import json
                import re
                
                # 提取JSON
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    data = json.loads(json_match.group(0))
                    return data
            
        except Exception as e:
            self.logger.error(f"內文分析失敗: {str(e)}")
        
        return None
    
    def batch_parse_emails(self, emails: List[EmailData]) -> List[EmailParsingResult]:
        """
        批次解析郵件
        
        Args:
            emails: 郵件清單
            
        Returns:
            List[EmailParsingResult]: 解析結果清單
        """
        results = []
        
        for email in emails:
            result = self.parse_email_with_body_analysis(email)
            results.append(result)
        
        self.logger.info(f"批次解析完成 - 總數: {len(emails)}, 成功: {sum(1 for r in results if r.is_success)}")
        
        return results
    
    def test_grok_connection(self) -> Dict[str, Any]:
        """測試 Grok 連接"""
        if not self.grok_client.is_enabled():
            return {
                "success": False,
                "message": "Grok 服務未啟用",
                "status": "disabled"
            }
        
        try:
            connection_test = self.grok_client.test_connection()
            
            if connection_test:
                return {
                    "success": True,
                    "message": "Grok 連接正常",
                    "status": "connected",
                    "client_status": self.grok_client.get_status()
                }
            else:
                return {
                    "success": False,
                    "message": "Grok 連接失敗",
                    "status": "disconnected",
                    "client_status": self.grok_client.get_status()
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"連接測試異常: {str(e)}",
                "status": "error"
            }
    
    def create_vendor_parser(self, vendor_code: str):
        """
        為特定廠商創建 Grok 解析器
        
        Args:
            vendor_code: 廠商代碼
            
        Returns:
            解析器實例或 None
        """
        if not self.is_grok_available():
            self.logger.debug(f"Grok 不可用，無法為 {vendor_code} 創建解析器")
            return None
        
        try:
            # 返回智能解析器，它可以處理所有廠商
            return self.smart_parser
        except Exception as e:
            self.logger.error(f"創建 {vendor_code} Grok 解析器失敗: {e}")
            return None
    
    def get_parsing_capabilities(self) -> Dict[str, Any]:
        """獲取解析能力資訊"""
        return {
            "grok_enabled": self.is_grok_available(),
            "supported_vendors": ["JCET", "GTK", "ETD", "LINGSEN", "XAHT"],
            "parsing_methods": {
                "grok_smart": "Grok智能解析",
                "traditional_fallback": "傳統後備解析",
                "body_analysis": "內文分析",
                "hybrid": "混合解析"
            },
            "features": [
                "智能廠商識別",
                "多種解析方式自動選擇",
                "內文產品型號提取",
                "解析信心分數",
                "詳細解析推理",
                "性能統計"
            ]
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """獲取性能統計"""
        smart_parser_stats = self.smart_parser.get_parsing_stats()
        
        return {
            "factory_stats": self.performance_stats.copy(),
            "smart_parser_stats": smart_parser_stats,
            "combined_stats": {
                "total_requests": self.performance_stats["total_requests"],
                "success_rate": self._calculate_success_rate(),
                "grok_usage_rate": self._calculate_grok_usage_rate(),
                "avg_response_time": self.performance_stats["avg_response_time"]
            }
        }
    
    def _calculate_success_rate(self) -> float:
        """計算成功率"""
        total = self.performance_stats["total_requests"]
        if total == 0:
            return 0.0
        return (self.performance_stats["successful_parses"] / total) * 100
    
    def _calculate_grok_usage_rate(self) -> float:
        """計算 Grok 使用率"""
        total = self.performance_stats["total_requests"]
        if total == 0:
            return 0.0
        return (self.performance_stats["grok_api_calls"] / total) * 100
    
    def _update_avg_response_time(self, response_time: float):
        """更新平均響應時間"""
        current_avg = self.performance_stats["avg_response_time"]
        total_requests = self.performance_stats["total_requests"]
        
        if total_requests == 1:
            self.performance_stats["avg_response_time"] = response_time
        else:
            # 計算新的平均值
            new_avg = ((current_avg * (total_requests - 1)) + response_time) / total_requests
            self.performance_stats["avg_response_time"] = new_avg
    
    def reset_performance_stats(self):
        """重置性能統計"""
        self.performance_stats = {
            "total_requests": 0,
            "successful_parses": 0,
            "failed_parses": 0,
            "avg_response_time": 0.0,
            "grok_api_calls": 0,
            "traditional_fallbacks": 0
        }
        
        self.smart_parser.reset_stats()
        self.logger.info("性能統計已重置")
    
    def create_parsing_report(self) -> Dict[str, Any]:
        """創建解析報告"""
        stats = self.get_performance_stats()
        capabilities = self.get_parsing_capabilities()
        connection_test = self.test_grok_connection()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "grok_connection": connection_test,
            "parsing_capabilities": capabilities,
            "performance_statistics": stats,
            "recommendations": self._generate_recommendations(stats)
        }
    
    def _generate_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """生成優化建議"""
        recommendations = []
        
        combined_stats = stats.get("combined_stats", {})
        success_rate = combined_stats.get("success_rate", 0)
        grok_usage_rate = combined_stats.get("grok_usage_rate", 0)
        avg_response_time = combined_stats.get("avg_response_time", 0)
        
        if success_rate < 80:
            recommendations.append("解析成功率較低，建議檢查郵件格式或調整解析規則")
        
        if grok_usage_rate < 30:
            recommendations.append("Grok使用率較低，可能需要調整分類器信心閾值")
        
        if avg_response_time > 10:
            recommendations.append("平均響應時間較長，建議優化提示詞或調整超時設定")
        
        if not recommendations:
            recommendations.append("解析性能良好，繼續保持當前設定")
        
        return recommendations