#!/usr/bin/env python3
"""
自動郵件處理器
負責新郵件的自動解析和檔案處理
"""

import json
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Any, Optional

from src.infrastructure.llm.unified_llm_client import UnifiedLLMClient
from src.infrastructure.adapters.database.models import EmailDB, AttachmentDB


class AutoEmailProcessor:
    """自動郵件處理器"""
    
    def __init__(self, database, logger):
        """
        初始化自動郵件處理器
        
        Args:
            database: 郵件資料庫實例
            logger: 日誌記錄器
        """
        self.database = database
        self.logger = logger
        self.unified_client = UnifiedLLMClient()
    
    async def auto_parse_new_emails(self, limit: int = 10):
        """
        自動解析新同步的郵件
        
        Args:
            limit: 一次處理的最大郵件數量
        """
        try:
            self.logger.info("開始自動解析新郵件...")
            
            # 查詢狀態為 pending 的郵件（新同步的郵件）
            with self.database.get_session() as session:
                pending_emails = session.query(EmailDB).filter_by(parse_status='pending').limit(limit).all()
                
                if not pending_emails:
                    self.logger.info("沒有待解析的新郵件")
                    return {
                        'success': True,
                        'parsed_count': 0,
                        'success_count': 0,
                        'failed_count': 0
                    }
                
                parsed_count = 0
                success_count = 0
                failed_count = 0
                
                for email_db in pending_emails:
                    try:
                        self.logger.info(f"自動解析郵件: {email_db.subject[:50]}...")
                        
                        # 使用統一 LLM 解析 - 添加超時保護
                        llm_result = None
                        try:
                            # 添加額外的超時保護層
                            import signal
                            import asyncio
                            from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
                            
                            def parse_with_timeout():
                                return self.unified_client.parse_email(
                                    subject=email_db.subject,
                                    body=email_db.body or "",
                                    sender=email_db.sender or ""
                                )
                            
                            # 使用線程池執行，最多等待45秒
                            with ThreadPoolExecutor(max_workers=1) as executor:
                                future = executor.submit(parse_with_timeout)
                                try:
                                    llm_result = future.result(timeout=45)
                                except FutureTimeoutError:
                                    self.logger.warning(f"LLM 解析超時 (45s): {email_db.subject[:30]}...")
                                    # 創建超時結果
                                    from src.infrastructure.llm.unified_llm_client import UnifiedLLMResult
                                    llm_result = UnifiedLLMResult(
                                        error_message="LLM 解析超時 (45秒)",
                                        is_success=False,
                                        llm_provider="timeout",
                                        llm_model="N/A"
                                    )
                                    
                        except Exception as llm_error:
                            self.logger.error(f"LLM 解析異常: {llm_error}")
                            from src.infrastructure.llm.unified_llm_client import UnifiedLLMResult
                            llm_result = UnifiedLLMResult(
                                error_message=f"LLM 解析異常: {str(llm_error)}",
                                is_success=False,
                                llm_provider="error",
                                llm_model="N/A"
                            )
                        
                        # 更新解析時間
                        current_time = datetime.now()
                        email_db.parsed_at = current_time
                        
                        # 如果 LLM 解析失敗，立即嘗試傳統解析器作為備援
                        if not llm_result.is_success:
                            self.logger.info(f"LLM 解析失敗，嘗試傳統解析器: {llm_result.error_message}")
                            try:
                                # 使用傳統解析器作為備援
                                from src.data_models.email_models import EmailData
                                from src.infrastructure.parsers.base_parser import ParserFactory
                                
                                email_data = EmailData(
                                    message_id=email_db.message_id,
                                    subject=email_db.subject,
                                    sender=email_db.sender,
                                    body=email_db.body or "",
                                    received_time=email_db.received_time
                                )
                                
                                parser_factory = ParserFactory()
                                vendor_result, parsing_result = parser_factory.parse_email(email_data)
                                
                                if parsing_result.is_success:
                                    self.logger.info(f"傳統解析器備援成功: {vendor_result.vendor_code}")
                                    # 轉換傳統解析器結果為 LLM 格式
                                    from src.infrastructure.llm.unified_llm_client import UnifiedLLMResult
                                    llm_result = UnifiedLLMResult(
                                        vendor_code=vendor_result.vendor_code,
                                        vendor_name=vendor_result.vendor_name,
                                        product_code=parsing_result.product_code or parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
                                        lot_number=parsing_result.lot_number,
                                        mo_number=parsing_result.mo_number,
                                        yield_rate=parsing_result.extracted_data.get('yield_value'),
                                        confidence_score=vendor_result.confidence_score,
                                        extraction_method="fallback",
                                        is_success=True,
                                        llm_provider="fallback",
                                        llm_model="traditional_parser"
                                    )
                                else:
                                    self.logger.warning(f"傳統解析器也失敗: {parsing_result.error_message}")
                                    
                            except Exception as fallback_error:
                                self.logger.error(f"傳統解析器備援失敗: {fallback_error}")
                        
                        if llm_result.is_success:
                            # 解析成功，更新所有欄位
                            email_db.vendor_code = llm_result.vendor_code
                            email_db.pd = llm_result.product_code
                            email_db.lot = llm_result.lot_number
                            email_db.mo = llm_result.mo_number  # 修復：添加 MO 欄位更新
                            email_db.yield_value = llm_result.yield_rate
                            email_db.extraction_method = llm_result.extraction_method or 'unified_llm'
                            email_db.parse_status = 'parsed'
                            email_db.parse_error = None
                            
                            # 更新 LLM 分析相關欄位
                            llm_analysis_data = {
                                'success': True,
                                'vendor_code': llm_result.vendor_code,
                                'vendor_name': llm_result.vendor_name,
                                'product_code': llm_result.product_code,
                                'lot_number': llm_result.lot_number,
                                'mo_number': llm_result.mo_number,  # 修復：添加 MO 欄位
                                'yield_rate': llm_result.yield_rate,
                                'confidence_score': llm_result.confidence_score,
                                'llm_provider': llm_result.llm_provider,
                                'llm_model': llm_result.llm_model,
                                'analysis_reasoning': llm_result.analysis_reasoning,
                                'analysis_timestamp': current_time.isoformat()
                            }
                            
                            email_db.llm_analysis_result = json.dumps(llm_analysis_data, ensure_ascii=False)
                            email_db.llm_analysis_timestamp = current_time
                            email_db.llm_service_used = llm_result.llm_provider
                            
                            success_count += 1
                            self.logger.info(f"自動解析成功: {email_db.subject[:30]}... -> {llm_result.vendor_code}")
                            
                            # 解析成功後自動執行檔案處理
                            await self.auto_process_after_parsing(email_db.id)
                            
                        else:
                            # 解析失敗
                            email_db.parse_status = 'failed'
                            email_db.parse_error = llm_result.error_message or '解析失敗'
                            email_db.extraction_method = llm_result.extraction_method or 'unified_llm'
                            
                            # 也更新 LLM 分析欄位（記錄失敗）
                            llm_analysis_data = {
                                'success': False,
                                'error_message': llm_result.error_message,
                                'llm_provider': llm_result.llm_provider,
                                'analysis_timestamp': current_time.isoformat()
                            }
                            
                            email_db.llm_analysis_result = json.dumps(llm_analysis_data, ensure_ascii=False)
                            email_db.llm_analysis_timestamp = current_time
                            email_db.llm_service_used = llm_result.llm_provider
                            
                            failed_count += 1
                            self.logger.warning(f"自動解析失敗: {email_db.subject[:30]}... -> {llm_result.error_message}")
                        
                        parsed_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"自動解析郵件 {email_db.id} 失敗: {e}")
                        # 標記為解析失敗
                        email_db.parse_status = 'failed'
                        email_db.parse_error = f'自動解析異常: {str(e)}'
                        email_db.parsed_at = datetime.now()
                        failed_count += 1
                
                # 提交所有變更
                session.commit()
                
                self.logger.info(f"自動解析完成: 處理 {parsed_count} 封郵件，成功 {success_count} 封，失敗 {failed_count} 封")
                
                return {
                    'success': True,
                    'parsed_count': parsed_count,
                    'success_count': success_count,
                    'failed_count': failed_count
                }
                
        except Exception as e:
            self.logger.error(f"自動解析新郵件失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'parsed_count': 0,
                'success_count': 0,
                'failed_count': 0
            }
    
    async def auto_process_after_parsing(self, email_id: int):
        """
        解析成功後自動執行檔案處理
        
        Args:
            email_id: 郵件ID
        """
        try:
            self.logger.info(f"解析成功，自動處理郵件 {email_id} 的檔案...")
            
            with self.database.get_session() as session:
                email = session.query(EmailDB).filter_by(id=email_id).first()
                if not email or not email.vendor_code:
                    self.logger.debug(f"郵件 {email_id} 不存在或無廠商代碼，跳過處理")
                    return
                
                # 檢查是否已經處理過
                if email.is_processed:
                    self.logger.debug(f"郵件 {email_id} 已處理過，跳過")
                    return
                
                vendor_code = email.vendor_code
                pd = email.pd or 'default'
                lot = email.lot or 'default'
                
                # 建立目標路徑 D:\temp\{pd}\{lot}
                temp_base_path = os.getenv('FILE_TEMP_BASE_PATH', 'D:\\temp')
                target_path = Path(temp_base_path) / pd / lot
                target_path.mkdir(parents=True, exist_ok=True)
                
                self.logger.info(f"自動處理郵件 {email_id}: {vendor_code} -> {target_path}")
                
                files_copied = 0
                
                # 處理附件
                attachments = session.query(AttachmentDB).filter_by(email_id=email_id).all()
                
                for attachment in attachments:
                    try:
                        if attachment.file_path and Path(attachment.file_path).exists():
                            source_file = Path(attachment.file_path)
                            target_file = target_path / attachment.filename
                            shutil.copy2(source_file, target_file)
                            files_copied += 1
                            self.logger.debug(f"複製附件: {attachment.filename} -> {target_file}")
                    except Exception as e:
                        self.logger.error(f"複製附件 {attachment.filename} 失敗: {e}")
                
                # 嘗試處理廠商檔案（如果有的話）
                try:
                    from src.infrastructure.adapters.file_handlers import FileHandlerFactory
                    
                    source_base_path = os.getenv('FILE_SOURCE_BASE_PATH', '\\\\192.168.1.60\\test_log')
                    factory = FileHandlerFactory(source_base_path)
                    mo = lot  # 使用 lot 作為 mo
                    
                    result = factory.process_vendor_files(
                        vendor_code=vendor_code,
                        mo=mo,
                        temp_path=str(target_path),
                        pd=pd,
                        lot=lot
                    )
                    
                    if result['success']:
                        self.logger.info(f"成功處理廠商檔案 {email_id}: {vendor_code}")
                        files_copied += result.get('files_copied', 0)
                    else:
                        self.logger.warning(f"處理廠商檔案失敗 {email_id}: {result.get('error')}")
                        
                except Exception as e:
                    self.logger.warning(f"無法處理廠商檔案 {email_id}: {e}")
                
                # 更新處理狀態
                if files_copied > 0:
                    email.is_processed = True
                    self.logger.info(f"自動處理完成: 郵件 {email_id}，複製了 {files_copied} 個檔案到 {target_path}")
                else:
                    self.logger.warning(f"郵件 {email_id} 沒有可處理的檔案")
                
                session.commit()
                
        except Exception as e:
            self.logger.error(f"自動處理郵件 {email_id} 檔案失敗: {e}")
    
    async def batch_parse_pending_emails(self, limit: int = 50):
        """
        批次解析所有待解析郵件（定期任務用）
        
        Args:
            limit: 一次處理的最大郵件數量
        """
        try:
            self.logger.info("開始定期批次解析待解析郵件...")
            
            with self.database.get_session() as session:
                # 查詢所有待解析郵件
                pending_emails = session.query(EmailDB).filter_by(parse_status='pending').limit(limit).all()
                
                if not pending_emails:
                    self.logger.debug("沒有待解析郵件")
                    return {
                        'success': True,
                        'message': '沒有待解析郵件',
                        'parsed_count': 0
                    }
                
                self.logger.info(f"找到 {len(pending_emails)} 封待解析郵件")
                
                # 調用自動解析方法
                result = await self.auto_parse_new_emails(limit)
                
                if result['success'] and result['parsed_count'] > 0:
                    self.logger.info(f"定期批次解析完成: 處理 {result['parsed_count']} 封，成功 {result['success_count']} 封")
                
                return result
                
        except Exception as e:
            self.logger.error(f"定期批次解析失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'parsed_count': 0
            }