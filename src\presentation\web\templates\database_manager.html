<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫管理</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/base.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/database_manager.css') }}">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <style>
        .search-results-info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #bee5eb;
            margin-top: 10px;
        }
        
        .sql-query-section {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .sql-query-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
        }
        .search-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .search-tips {
            background-color: #e7f3ff;
            padding: 12px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        .search-tips p {
            margin: 0;
            color: #495057;
            font-size: 14px;
        }
        .query-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: flex-end;
        }
        .query-container textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ffb74d;
            border-radius: 4px;
            font-size: 14px;
            font-family: monospace;
        }
        .query-container textarea:focus {
            outline: none;
            border-color: #ff9800;
            box-shadow: 0 0 0 0.2rem rgba(255, 152, 0, 0.25);
        }
        .query-suggestions {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .query-suggestions h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        .suggestion-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn-small:hover {
            background-color: #0056b3;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
            margin-top: 10px;
        }
        
        /* 額外的表格邊框強化 */
        table.dataTable.display {
            border-collapse: collapse !important;
        }
        
        table.dataTable.display thead th,
        table.dataTable.display tbody td {
            border: 1px solid #dee2e6 !important;
        }
        
        table.dataTable.display tbody tr {
            border-bottom: 1px solid #dee2e6 !important;
        }
        
        /* 確保操作按鈕容器有適當的內邊距 */
        .action-buttons {
            white-space: nowrap;
            padding: 4px;
        }
        
        /* 改善標籤顯示 */
        .extraction-method-tag,
        .parse-status-tag,
        .vendor-code-tag {
            display: inline-block;
            margin: 2px;
            font-weight: 500;
            border: 1px solid rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="db-manager-container">
        <!-- 頂部導航 -->
        <header class="db-header">
            <div class="header-left">
                <h1>🗄️ 資料庫管理</h1>
                <a href="/" class="btn btn-secondary">
                    <span class="btn-icon">◀</span>
                    <span class="btn-text">返回郵件收件夾</span>
                </a>
            </div>
            <div class="header-right">
                <div class="db-info">
                    <span class="info-item">資料庫: <strong>{{ db_path }}</strong></span>
                    <span class="info-item">大小: <strong id="db-size">計算中...</strong></span>
                </div>
            </div>
        </header>

        <!-- 表格選擇與搜尋 -->
        <div class="table-selector">
            <label for="table-select">選擇資料表：</label>
            <select id="table-select" class="form-select">
                <option value="">-- 請選擇資料表 --</option>
                <option value="emails">emails - 郵件</option>
                <option value="senders">senders - 寄件者</option>
                <option value="attachments">attachments - 附件</option>
                <option value="email_process_status">email_process_status - 處理狀態</option>
            </select>
            <button id="refresh-btn" class="btn btn-primary">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">重新整理</span>
            </button>
            <button id="export-csv-btn" class="btn btn-secondary" disabled>
                <span class="btn-icon">📥</span>
                <span class="btn-text">匯出 CSV</span>
            </button>
        </div>

        <!-- 搜尋與查詢區域 -->
        <div class="sql-query-section">
            <h3>🔍 搜尋資料</h3>
            <div class="search-container">
                <input type="text" id="search-input" placeholder="輸入搜尋關鍵字（例如：tong、郵件主旨、廠商代碼...）" class="form-input">
                <button id="search-btn" class="btn btn-primary">搜尋</button>
                <button id="clear-search-btn" class="btn btn-secondary">清除</button>
                <button id="advanced-search-toggle-btn" class="btn btn-secondary" style="margin-left: 10px;">
                    🔽 進階搜尋
                </button>
                <button id="select-all-search-btn" class="btn btn-info" style="margin-left: 10px;">
                    ☑️ 全選搜尋結果
                </button>
            </div>
            <div class="search-tips">
                <p>💡 <strong>搜尋提示：</strong>輸入「tong」可找到「<EMAIL>」，可搜尋寄件者、主旨、內容、廠商代碼等多個欄位</p>
            </div>
            <div id="search-results-info" class="search-results-info hidden">
                <span id="search-results-text"></span>
            </div>
            
            <!-- 隱藏的SQL查詢區域 -->
            <div style="display: none;">
                <textarea id="sql-query" placeholder="輸入 SELECT 查詢語句..." rows="3"></textarea>
                <button id="execute-query-btn" class="btn btn-primary">執行查詢</button>
            </div>
            
            <div class="query-suggestions">
                <h4>常用查詢範例：</h4>
                <div class="suggestion-buttons">
                    <button class="btn btn-small" onclick="setQuery('SELECT * FROM emails WHERE extraction_method = \'llm\' ORDER BY parsed_at DESC')">LLM 解析的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT * FROM emails WHERE extraction_method = \'traditional\' ORDER BY parsed_at DESC')">傳統解析的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT vendor_code, extraction_method, COUNT(*) as count FROM emails WHERE parse_status = \'parsed\' GROUP BY vendor_code, extraction_method ORDER BY vendor_code, extraction_method')">各廠商解析方法統計</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT * FROM emails WHERE parse_status = \'failed\' ORDER BY received_time DESC')">解析失敗的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT vendor_code, COUNT(*) as total, COUNT(CASE WHEN parse_status = \'parsed\' THEN 1 END) as parsed FROM emails GROUP BY vendor_code')">廠商解析成功率</button>
                </div>
            </div>
            <div id="query-error" class="error-message hidden"></div>
        </div>

        <!-- 表格資訊 -->
        <div id="table-info" class="table-info hidden">
            <h3 id="table-name">資料表名稱</h3>
            <div class="table-stats">
                <span>總記錄數: <strong id="record-count">0</strong></span>
                <span>欄位數: <strong id="column-count">0</strong></span>
            </div>
        </div>

        <!-- 進階搜尋面板 -->
        <div id="advanced-search-panel" class="advanced-search-panel hidden" style="
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        ">
            <h4 style="margin-top: 0; color: #495057;">🔍 進階搜尋篩選</h4>
            <div class="search-filters" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div>
                    <label>寄件者：</label>
                    <input type="text" id="filter-sender" class="form-input" placeholder="搜尋寄件者...">
                </div>
                <div>
                    <label>主旨：</label>
                    <input type="text" id="filter-subject" class="form-input" placeholder="搜尋主旨...">
                </div>
                <div>
                    <label>廠商代碼：</label>
                    <select id="filter-vendor" class="form-input">
                        <option value="">-- 全部 --</option>
                        <option value="JCET">JCET</option>
                        <option value="CTK">CTK</option>
                        <option value="TSM">TSM</option>
                    </select>
                </div>
                <div>
                    <label>解析狀態：</label>
                    <select id="filter-parse-status" class="form-input">
                        <option value="">-- 全部 --</option>
                        <option value="parsed">已解析</option>
                        <option value="failed">解析失敗</option>
                        <option value="pending">待解析</option>
                    </select>
                </div>
                <div>
                    <label>產品編號：</label>
                    <input type="text" id="filter-product" class="form-input" placeholder="搜尋產品編號...">
                </div>
                <div>
                    <label>MO編號：</label>
                    <input type="text" id="filter-mo" class="form-input" placeholder="搜尋MO編號...">
                </div>
            </div>
            <div style="margin-top: 15px;">
                <button id="apply-filters-btn" class="btn btn-primary" style="margin-right: 10px;">套用篩選</button>
                <button id="reset-filters-btn" class="btn btn-secondary">重置</button>
            </div>
        </div>


        <!-- 資料表格容器 -->
        <div id="data-container" class="data-container">
            <div id="loading" class="loading hidden">
                <div class="loading-spinner"></div>
                <p>載入資料中...</p>
            </div>
            <table id="data-table" class="display" style="width:100%">
                <!-- 動態生成 -->
            </table>
        </div>

        <!-- 詳情模態框 -->
        <div id="detail-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">記錄詳情</h3>
                    <button class="close-btn" onclick="closeDetailModal()">✕</button>
                </div>
                <div id="modal-body" class="modal-body">
                    <!-- 動態生成 -->
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeDetailModal()">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <!-- 自定義 JS -->
    <script src="{{ url_for('static', filename='js/database_manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/database_manager_extensions.js') }}"></script>
</body>
</html>